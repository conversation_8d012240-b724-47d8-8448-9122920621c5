# 需求文档

## 介绍

当前TKE在删除Service/Ingress资源时，会自动删除由TKE创建的CLB实例。但在某些场景下，用户希望保留CLB实例以便后续复用，避免重新创建CLB带来的成本和配置工作。

本需求旨在为Service和Ingress资源提供一个注解选项，允许用户在删除资源时保留TKE自动创建的CLB实例。

## 需求

### 需求 1 - Service删除时保留CLB

**用户故事：** 作为TKE用户，当我删除一个使用自动创建CLB的Service时，我希望能够通过注解配置选择保留CLB实例，以便后续其他Service可以复用该CLB。

#### 验收标准

1. When Service配置了保留CLB注解且该Service使用TKE自动创建的CLB时，删除Service时系统应当保留CLB实例不被删除。
2. When Service配置了保留CLB注解时，删除Service时系统应当清理Service相关的监听器和后端配置，但保留CLB实例本身。
3. When Service配置了保留CLB注解时，删除Service时系统应当清理CLB上与该Service相关的TKE标签，但保留CLB实例。
4. When Service未配置保留CLB注解时，删除Service时系统应当按照原有逻辑删除整个CLB实例。
5. When Service使用已有CLB（非TKE自动创建）时，保留CLB注解应当不影响现有删除逻辑。

### 需求 2 - Ingress删除时保留CLB

**用户故事：** 作为TKE用户，当我删除一个使用自动创建CLB的Ingress时，我希望能够通过注解配置选择保留CLB实例，以便后续其他Ingress可以复用该CLB。

#### 验收标准

1. When Ingress配置了保留CLB注解且该Ingress使用TKE自动创建的CLB时，删除Ingress时系统应当保留CLB实例不被删除。
2. When Ingress配置了保留CLB注解时，删除Ingress时系统应当清理Ingress相关的监听器和后端配置，但保留CLB实例本身。
3. When Ingress配置了保留CLB注解时，删除Ingress时系统应当清理CLB上与该Ingress相关的TKE标签，但保留CLB实例。
4. When Ingress未配置保留CLB注解时，删除Ingress时系统应当按照原有逻辑删除整个CLB实例。
5. When Ingress使用已有CLB（非TKE自动创建）时，保留CLB注解应当不影响现有删除逻辑。

### 需求 3 - 注解设计和兼容性

**用户故事：** 作为TKE用户，我希望保留CLB的注解设计简单易用，且与现有的删除保护等功能兼容。

#### 验收标准

1. When Service/Ingress同时配置了删除保护注解和保留CLB注解时，删除保护注解应当优先生效，阻止资源删除。
2. When Service/Ingress配置了保留CLB注解时，系统应当在删除完成后在事件中记录CLB被保留的信息。
3. When 保留CLB注解配置为无效值时，系统应当记录错误事件并按照默认逻辑处理（删除CLB）。
4. When 保留的CLB实例存在时，后续创建的Service/Ingress应当能够通过已有CLB注解正常复用该CLB。

### 需求 4 - 文档和用户体验

**用户故事：** 作为TKE用户，我希望能够清楚地了解如何使用保留CLB功能，以及该功能的限制和注意事项。

#### 验收标准

1. When 用户查看注解文档时，系统应当提供保留CLB注解的详细说明和使用示例。
2. When 用户使用保留CLB功能时，系统应当在相关事件中提供清晰的状态说明。
3. When 保留CLB功能与其他功能冲突时，系统应当提供明确的错误信息和解决建议。
