# 技术方案设计

## 架构概述

本方案在现有的service-controller和ingress-controller基础上，通过新增注解来控制CLB删除行为。主要涉及以下组件：

- Service Controller: 处理Service删除时的CLB保留逻辑
- Ingress Controller: 处理Ingress删除时的CLB保留逻辑  
- 注解系统: 新增保留CLB相关注解定义
- CLB删除逻辑: 修改现有删除流程以支持保留选项

## 技术栈

- **语言**: Go 1.19+
- **框架**: Kubernetes Controller Runtime
- **云服务**: 腾讯云CLB API
- **存储**: Kubernetes etcd (注解存储)

## 技术选型

### 注解设计

新增以下注解：

**Service注解:**
- `service.cloud.tencent.com/preserve-clb-on-deletion`: bool类型，控制删除Service时是否保留CLB

**Ingress注解:**  
- `ingress.cloud.tencent.com/preserve-clb-on-deletion`: bool类型，控制删除Ingress时是否保留CLB

### 实现方案

#### 1. 注解定义和注册

在注解注册系统中新增保留CLB注解的定义，包括：
- 注解键名定义
- 类型验证（bool类型）
- 文档说明
- 示例配置

#### 2. Service删除逻辑修改

修改 `pkg/plugin/tencent/main.go` 中的 `EnsureLoadBalancerDeleted` 函数：

```go
// 伪代码示例
func EnsureLoadBalancerDeleted(service service_wrapper.ServiceWrapper, loadBalancerContext *LoadBalancerContext) error {
    // 检查是否配置了保留CLB注解
    if shouldPreserveCLB(service) && lb.IsAutoCreatedByTKE() {
        // 保留CLB，只清理监听器和标签
        return preserveCLBOnDeletion(service, loadBalancerContext)
    }
    
    // 原有删除逻辑
    return originalDeletionLogic(service, loadBalancerContext)
}
```

#### 3. Ingress删除逻辑修改

修改 `staging/ingress-controller/pkg/plugins/tencent/clb.go` 中的 `EnsureDeleteLoadBalancer` 函数，添加类似的保留逻辑。

#### 4. CLB保留处理流程

当配置保留CLB时，执行以下操作：
1. 清理CLB上的监听器配置
2. 清理CLB上的后端RS配置  
3. 清理TKE相关的云标签
4. 保留CLB实例本身
5. 记录保留事件

## 数据库/接口设计

### 注解接口扩展

扩展现有的注解接口：

```go
// Service注解接口
type Interface interface {
    // ... 现有方法
    PreserveCLBOnDeletion() *bool
}

// Ingress注解接口  
type Interface interface {
    // ... 现有方法
    PreserveCLBOnDeletion() *bool
}
```

### CLB操作接口

新增CLB保留相关的操作接口：

```go
// 保留CLB时的清理操作
func PreserveCLBOnServiceDeletion(service ServiceWrapper, lbContext *LoadBalancerContext) error
func PreserveCLBOnIngressDeletion(ingress types.Ingress, lbContext *LoadBalancerContext) error

// 清理CLB配置但保留实例
func CleanCLBConfigButPreserveInstance(lbContext *LoadBalancerContext) error
```

## 测试策略

### 单元测试
- 注解解析逻辑测试
- CLB保留逻辑测试
- 边界条件测试（无效注解值等）

### 集成测试  
- Service删除保留CLB场景测试
- Ingress删除保留CLB场景测试
- 与删除保护功能的兼容性测试
- 保留CLB后复用场景测试

### 回归测试
- 确保未配置保留注解时原有逻辑不受影响
- 确保使用已有CLB场景不受影响

## 安全性

### 权限控制
- 保留CLB功能不需要额外的RBAC权限
- 依赖现有的CLB操作权限

### 数据安全
- 保留CLB时确保敏感配置被正确清理
- 避免CLB配置泄露到其他租户

### 审计日志
- 记录CLB保留操作的审计事件
- 提供详细的操作日志用于问题排查

## 兼容性考虑

### 向后兼容
- 未配置新注解时保持原有行为不变
- 新注解为可选配置，不影响现有用户

### 功能兼容
- 与删除保护功能兼容（删除保护优先级更高）
- 与强制删除功能兼容
- 与多集群功能兼容

### 版本兼容
- 支持从v2.7.0版本开始
- 向前兼容旧版本的注解配置

## 监控和可观测性

### 指标监控
- 新增CLB保留操作的成功/失败指标
- 监控保留CLB的数量和状态

### 事件记录
- 记录CLB保留成功事件
- 记录CLB保留失败事件及原因
- 提供详细的操作日志

### 告警机制
- CLB保留操作失败告警
- 保留CLB数量异常告警
