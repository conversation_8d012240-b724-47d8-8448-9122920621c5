package annotation

import (
	"git.woa.com/kateway/pkg/k8s/k8sutil/annotation"
)

func init() {
	service = &ServiceRegistry{
		Registry: annotation.NewRegistry(defaultPrefix, exampleTemplate),
	}

	service.Register([]annotation.Item{
		// 控制器相关
		{
			Name:     "负载均衡 ID",
			Key:      "loadbalance-id",
			Prefix:   k8sPrefix,
			Type:     annotation.StringType,
			Public:   true,
			ReadOnly: true,
			Document: `
只读注解，提供当前 Service 引用的负载均衡 LoadBalanceId。您可以在腾讯云 CLB 控制台查看与集群在同一 VPC 下的 CLB 实例 ID。
`,
			RequireVersion: ">=v1.0.0",
			Commit:         "2919f6ffe164cbe0d021ccc750f6291848432407",
			Author:         "howardxiao(肖华源)",
		},
		// 负载均衡属性
		{
			Name:   "复用已有负载均衡",
			Key:    "tke-existed-lbid",
			Prefix: k8sPrefix,
			Type:   annotation.StringType,
			Public: true,
			Document: `
使用已存在的 CLB，需注意不同使用方式对腾讯云标签的影响。
详情参考 [Service 使用已有 CLB](https://cloud.tencent.com/document/product/457/45491)。
`,
			ExampleValue:   "lb-6swtxxxx",
			RequireVersion: ">=v1.0.0",
		},
		{
			Name:   "创建内网负载均衡",
			Key:    "qcloud-loadbalancer-internal-subnetid",
			Prefix: k8sPrefix,
			Type:   annotation.StringType,
			Public: true,
			Document: `
在指定子网创建内网负载均衡。
`,
			ExampleValue:   "subnet-xxx",
			RequireVersion: ">=v1.0.0",
			Commit:         "2919f6ffe164cbe0d021ccc750f6291848432407",
			Author:         "howardxiao(肖华源)",
		},
		{
			Name:   "指定付费类型",
			Key:    "qcloud-loadbalancer-internet-charge-type",
			Prefix: k8sPrefix,
			Type:   annotation.EnumType,
			Public: true,
			Document: `
负载均衡的付费类型，当前仅在创建时支持配置，创建后不支持修改付费类型，创建后修改本注解无效。
指定创建负载均衡时，负载均衡的付费类型。请配合 service.kubernetes.io/qcloud-loadbalancer-internet-max-bandwidth-out 注解一起使用。

**可选值：**
- BANDWIDTH_POSTPAID_BY_HOUR：按带宽按小时后计费
- TRAFFIC_POSTPAID_BY_HOUR：按流量按小时后计费
`,
			ExampleValue: "BANDWIDTH_POSTPAID_BY_HOUR",
			ExampleExtra: []string{
				"service.kubernetes.io/qcloud-loadbalancer-internet-max-bandwidth-out: '10'",
			},
			RequireVersion: ">=v1.0.0",
			Commit:         "2919f6ffe164cbe0d021ccc750f6291848432407",
			Author:         "howardxiao(肖华源)",
		},
		{
			Name:   "指定公网带宽上限",
			Key:    "qcloud-loadbalancer-internet-max-bandwidth-out",
			Prefix: k8sPrefix,
			Type:   annotation.IntType,
			Public: true,
			Document: `
CLB 带宽设置，当前仅在创建时支持配置，创建后不支持修改带宽，创建后修改本注解无效。
指定创建负载均衡时，负载均衡的最大出带宽，仅对公网属性的 LB 生效。需配合 service.kubernetes.io/qcloud-loadbalancer-internet-charge-type 注解一起使用。

值域： [1, 2048] Mbps。
`,
			ExampleValue: "10",
			ExampleExtra: []string{
				`service.kubernetes.io/qcloud-loadbalancer-internet-charge-type: 'TRAFFIC_POSTPAID_BY_HOUR'`,
			},
			RequireVersion: ">=v1.0.0",
			Commit:         "2919f6ffe164cbe0d021ccc750f6291848432407",
			Author:         "howardxiao(肖华源)",
		},
		{
			Name:   "负载均衡拓展参数",
			Key:    "service.extensiveParameters",
			Prefix: k8sPrefix,
			Type:   annotation.JSONType,
			Public: true,
			Document: `
指定 CLB 创建时的参数，当前仅在创建时支持配置，创建后不支持修改，创建后修改本注解无效。
详情参考 [创建负载均衡实例](https://cloud.tencent.com/document/product/214/30692) 为创建负载均衡追加自定义参数。
`,
			ExampleValue:   `{"AddressIPVersion":"IPV4","InternetAccessible":{"InternetChargeType":"TRAFFIC_POSTPAID_BY_HOUR","InternetMaxBandwidthOut":10}}`,
			RequireVersion: ">=v1.0.0",
			Commit:         "2919f6ffe164cbe0d021ccc750f6291848432407",
			Author:         "howardxiao(肖华源)",
		},
		{
			Name:   "安全组默认放通",
			Key:    "pass-to-target",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
是否启用 CLB 安全组默认放通的能力，CLB 和 CVM 之间默认放通，来自 CLB 的流量只需通过 CLB 上安全组的校验。

**注意：**
- 请查看 CLB 使用安全组的[使用限制](https://cloud.tencent.com/document/product/214/14733)。
- 通常需要配合绑定安全组的能力。对应 Annotation 为：service.cloud.tencent.com/security-groups。
- 对于 [Service 使用已有 CLB](https://cloud.tencent.com/document/product/457/45491) 的场景，若多个 Service 声明了不同的放通配置，会有逻辑冲突的问题。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v1.8.3",
			Commit:         "e060740aae6b7f9a7ebdd5e81d25139cfb75cf5d",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "绑定安全组",
			Key:    "security-groups",
			Type:   annotation.StringType,
			Public: true,
			Document: `
为 CLB 绑定安全组，单个 CLB 最多可绑定5个安全组。

**注意：**
- 请查看 CLB 使用安全组的[使用限制](https://cloud.tencent.com/document/product/214/14733)。
- 通常需要配合安全组默认放通的能力，CLB 和 CVM 之间默认放通，来自 CLB 的流量只需通过 CLB 上安全组的校验。对应 Annotation 为：service.cloud.tencent.com/pass-to-target。
- 对于 [Service 使用已有 CLB](https://cloud.tencent.com/document/product/457/45491) 的场景，若多个 Service 声明了不同的安全组，会有逻辑冲突的问题。
`,
			ExampleValue: "sg-xxxxx,sg-yyyyy",
			ExampleExtra: []string{
				"service.cloud.tencent.com/pass-to-target: 'true'",
			},
			RequireVersion: ">=v1.8.3",
			Commit:         "e060740aae6b7f9a7ebdd5e81d25139cfb75cf5d",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "修改保护",
			Key:    "modification-protection",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
配置 CLB 修改保护，开启后将无法通过 CLB 控制台或 API 修改负载均衡实例的属性。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v1.7.3",
			Commit:         "28649e61e7a3b252333443c4d5b720f0bd0dbf93",
			Author:         "misakazhou(周宏宇)",
		},

		// 配置监听器
		{
			Name:   "监听器拓展参数",
			Key:    "service.listenerParameters",
			Prefix: k8sPrefix,
			Type:   annotation.JSONType,
			Public: true,
			Document: `
指定 CLB 创建监听器时的参数，当前仅在创建时支持配置，创建后不支持修改，创建后修改本注解无效。
`,
			RequireVersion: ">=v1.1.0",
			Commit:         "6606afef54ce83d6911f9ab642bf7adca9027c50",
			Author:         "howardxiao(肖华源)",
		},
		{
			Name:   "使用拓展配置",
			Key:    "tke-service-config",
			Type:   annotation.StringType,
			Public: true,
			Document: `
通过 tke-service-config 配置负载均衡 CLB。
详情参考 [Service 负载均衡配置](https://cloud.tencent.com/document/product/457/45490)。
`,
			ExampleValue:   "config-name",
			RequireVersion: ">=v1.3.0",
			Commit:         "2fcada0eb4efdc75fc6a661344a32a29d8cf527c",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "使用自动生成拓展配置",
			Key:    "tke-service-config-auto",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
通过该注解可自动创建 TkeServiceConfig，然后可按需修改配置。
详情参考 [Service 与 TkeServiceConfig 关联行为](https://cloud.tencent.com/document/product/457/45490#service-.E4.B8.8E-tkeserviceconfig-.E5.85.B3.E8.81.94.E8.A1.8C.E4.B8.BA)。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v1.3.0",
			Commit:         "2fcada0eb4efdc75fc6a661344a32a29d8cf527c",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "使用拓展协议",
			Key:    "specify-protocol",
			Type:   annotation.JSONType,
			Public: true,
			Document: `
支持通过注解为指定的监听端口配置 TCP、UDP、TCP SSL、HTTP、HTTPS。
详情参考 [Service 扩展协议](https://cloud.tencent.com/document/product/457/51259)。

`,
			ExampleValue:   `{"80":{"protocol":["HTTPS"],"hosts":{"a.tencent.com":{"tls":"cert-secret-a"},"b.tencent.com":{"tls":"cert-secret-b"}}}}`,
			RequireVersion: ">=v1.5.0",
			Commit:         "0cb929da25e89cbc5e1816ffb976f89c90e2c90d",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "开启防止回环",
			Key:    "prevent-loopback",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
是否开启防止回环，开启后将修改健康探测源模式，采用**********/10网段作为探测源。
详情参考 [健康检查探测标识](https://cloud.tencent.com/document/product/214/6097#.E5.81.A5.E5.BA.B7.E6.A3.80.E6.9F.A5.E6.8E.A2.E6.B5.8B.E6.A0.87.E8.AF.86)。
`,
			ExampleValue:   "false",
			RequireVersion: ">=v1.7.3",
			Commit:         "28649e61e7a3b252333443c4d5b720f0bd0dbf93",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "会话保持回退",
			Key:  "session-affinity-fallback",
			Type: annotation.BoolType,
			Document: `
默认为开启状态，只有当升级后为了兼容存量服务，才会由平台侧设置为"false"。
`,
			ExampleValue:   "false",
			RequireVersion: ">=v2.5.1",
			Author:         "lwbowenyan(颜博文)",
		},

		// 后端
		{
			Name:   "开启直连",
			Key:    "direct-access",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
是否开启直连，负载均衡绑定 pod IP。
详情参考 [使用 LoadBalancer 直连 Pod 模式 Service](https://cloud.tencent.com/document/product/457/41897)。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v1.3.0",
			Commit:         "2fcada0eb4efdc75fc6a661344a32a29d8cf527c",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "指定后端标签",
			Key:    "qcloud-loadbalancer-backends-label",
			Prefix: k8sPrefix,
			Type:   annotation.StringType,
			Public: true,
			Document: `
指定标签设置负载均衡后端绑定的节点。
详情参考 [指定接入层后端](https://cloud.tencent.com/document/product/457/45492#.E6.8C.87.E5.AE.9A.E6.8E.A5.E5.85.A5.E5.B1.82.E5.90.8E.E7.AB.AF)。
`,
			ExampleValue:   "group=access-layer",
			RequireVersion: ">=v1.0.0",
			Commit:         "2919f6ffe164cbe0d021ccc750f6291848432407",
			Author:         "howardxiao(肖华源)",
		},
		{
			Name:   "自定义后端权重",
			Key:    "lb-rs-weight",
			Type:   annotation.JSONType, // TODO: 复杂类型字段说明
			Public: true,
			Document: `
用来支持自定义后端权重，可以设定默认权重以及有状态服务的权重。
`,
			ExampleValue:   `{"defaultWeight":10,"groups":[{"key":{"proto":"TCP","port":80},"statefulSets":[{"name":"ti-ai-gateway-gz-shared-st-v1","weights":[{"weight":0,"podIndexes":[0]}]},{"name":"ti-ai-gateway-gz-shared-st-v2","weights":[{"weight":10,"podIndexes":[0]}]}]}]}`,
			RequireVersion: ">=v1.6.0",
			Commit:         "2d9efd5d820f60ad8f62e730365309b7f4bc2d20",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "跳过 readiness-gate",
			Key:    "readiness-gate-skip",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
开启后，控制器健康检查时，将跳过 readiness-gate 的检查。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v1.8.1",
			Commit:         "2bb0c7c8e89f4ed2138d6ebad0ddefe1758cf2e2",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:   "优雅停机",
			Key:    "enable-grace-shutdown",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
支持 CLB 直连模式的优雅停机。Pod 被删除，此时 Pod 里有 DeletionTimestamp，且状态置为 Terminating。此时调整 CLB 到该 Pod 的权重为 0。
仅在直连模式下支持，需要配合使用 service.cloud.tencent.com/direct-access。
v2.2.0 版本开始废弃，默认开启。
详情参考 [Service 优雅停机](https://cloud.tencent.com/document/product/457/60064)。
`,
			ExampleValue: "true",
			ExampleExtra: []string{
				`service.cloud.tencent.com/direct-access: 'true'`,
			},
			RequireVersion: ">=v1.5.0",
			Commit:         "0cb929da25e89cbc5e1816ffb976f89c90e2c90d",
			Author:         "gokutang(唐家伟)",
		},
		{
			Name:   "优雅停机 tkex",
			Key:    "enable-grace-shutdown-tkex",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
支持 CLB 直连模式的优雅退出。Endpoint 对象中 endpoints 是否 not-ready，将 not-ready 的 CLB 后端权重置为 0。
仅在直连模式下支持，需要配合使用 service.cloud.tencent.com/direct-access。
v2.2.0 版本开始废弃，默认开启。
详情参考 [Service 优雅停机](https://cloud.tencent.com/document/product/457/60064)。
`,
			ExampleValue: "true",
			ExampleExtra: []string{
				`service.cloud.tencent.com/direct-access: 'true'`,
			},
			RequireVersion: ">=v1.5.0",
			Commit:         "0cb929da25e89cbc5e1816ffb976f89c90e2c90d",
			Author:         "gokutang(唐家伟)",
		},
		{
			Name:   "优雅删除",
			Key:    "enable-grace-deletion",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
开启后，运行时等待控制器将所有 Pod 的权重调整为 0 后，再删除后端。
仅在直连模式下支持，需要配合使用 service.cloud.tencent.com/direct-access。
详情参考 [容器服务 Pod 优雅删除](https://cloud.tencent.com/document/product/457/109977)。
`,
			ExampleValue: "true",
			ExampleExtra: []string{
				`service.cloud.tencent.com/direct-access: 'true'`,
			},
			RequireVersion: ">=v2.4.0",
			Author:         "foxzhong(钟华)",
		},

		// 多集群
		{
			Name: "后端管理模式",
			Key:  "backend-management-mode",
			Type: annotation.EnumType,
			Document: `
后端管理模式。

**可选值：**
- tag(>=v2.3.0)：根据标签进行后端管理
`,
			ExampleValue:   "tag",
			RequireVersion: ">=v2.3.0",
			Commit:         "b99c72928c5c2feb68a2ef9232bbb03137dfec75",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "只管理后端",
			Key:  "backend-manage-only",
			Type: annotation.BoolType,
			Document: `
只负责管理后端，绑定、解绑、调整权重。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v2.3.0",
			Commit:         "e4f067dadac5579142b75d23347718614595668b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "被其他集群管理",
			Key:  "from-other-cluster",
			Type: annotation.StringType,
			Document: `
在多集群场景下，子集群用该字段记录父集群ID。
`,
			ExampleValue:   "cls-xxx",
			RequireVersion: ">=v2.3.0",
			Commit:         "e4f067dadac5579142b75d23347718614595668b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "tke 管理",
			Key:  "tke-management",
			Type: annotation.BoolType,
			Document: `
在多集群场景下，子集群用该字段记录是否被 tke 管理。
`,
			ExampleValue:   "true",
			RequireVersion: ">=v2.3.0",
			Commit:         "e4f067dadac5579142b75d23347718614595668b",
			Author:         "misakazhou(周宏宇)",
		},

		// 跨域
		{
			Name: "指定混合云类型",
			Key:  "hybrid-type",
			Type: annotation.EnumType,
			Document: `
指定混合云类型。

**可选值：**
- PVGW(>=v1.7.4)：私有网络对等连接
- CCN(>=v2.0.0)：云联网
`,
			ExampleValue:   "CCN",
			RequireVersion: ">=v1.7.4",
			Commit:         "2a7031ad4c6df01cbf8d4cc2095c0de02b8a3e3b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "配置 snat",
			Key:  "snat-pro-info",
			Type: annotation.JSONType,
			Document: `
指定 SNAT 配置，子网列表。
		`,
			ExampleValue:   `{"snatIps":[{"subnetId":"subnet-xxx"}]}`,
			RequireVersion: ">=v1.7.4",
			Commit:         "2a7031ad4c6df01cbf8d4cc2095c0de02b8a3e3b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "指定跨域地域 ID",
			Key:  "cross-region-id",
			Type: annotation.StringType,
			Document: `
指定跨域地域 ID。
`,
			ExampleValue:   "ap-guangzhou",
			RequireVersion: ">=v1.7.4",
			Commit:         "2a7031ad4c6df01cbf8d4cc2095c0de02b8a3e3b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "指定跨域 VPCID",
			Key:  "cross-vpc-id",
			Type: annotation.StringType,
			Document: `
指定跨域 VPCID。
`,
			ExampleValue:   "vpc-xxx",
			RequireVersion: ">=v1.7.4",
			Commit:         "2a7031ad4c6df01cbf8d4cc2095c0de02b8a3e3b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "指定跨域类型",
			Key:  "cross-type",
			Type: annotation.EnumType,
			Document: `
指定跨域类型。

**可选值：**
- ManagerOnly
- CrossTarget
- CCN
- PVGW
`,
			ExampleValue:   "PVGW-PRO",
			RequireVersion: ">=v1.7.4",
			Commit:         "2a7031ad4c6df01cbf8d4cc2095c0de02b8a3e3b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "指定目标跨域地域 ID",
			Key:  "target-cross-region-id",
			Type: annotation.StringType,
			Document: `
指定目标跨域地域 ID。
`,
			ExampleValue:   "ap-guangzhou",
			RequireVersion: ">=v2.3.0",
			Commit:         "e4f067dadac5579142b75d23347718614595668b",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name: "指定目标跨域 VPCID",
			Key:  "target-cross-vpc-id",
			Type: annotation.StringType,
			Document: `
指定目标跨域 VPCID。
`,
			ExampleValue:   "vpc-xxx",
			RequireVersion: ">=v2.3.0",
			Commit:         "e4f067dadac5579142b75d23347718614595668b",
			Author:         "misakazhou(周宏宇)",
		},

		// 内部
		{
			Name:     "负载均衡类型",
			Key:      "loadbalance-type",
			Prefix:   k8sPrefix,
			Type:     annotation.EnumType,
			ReadOnly: true,
			Document: `
只读注解，提供当前 Service 引用的负载均衡 LoadBalanceType。
`,
			RequireVersion: ">=v1.4.0",
			Commit:         "60ddbac0b6cf3f33aa67960a607dfb1b9a6c3cbc",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:     "client-token",
			Key:      "client-token",
			Type:     annotation.StringType,
			ReadOnly: true,
			Document: `
只读注解，记录创建负载均衡时使用的 client-token，用来保证幂等性，防止重复创建（为降低误删风险，禁止创建资源时，指定其他负载均衡已使用的 client-token 注解）。
`,
			RequireVersion: ">=v2.2.1",
			Commit:         "806a46699e6c14699f7c9051a7fe8b00b6f92f34",
			Author:         "irvingliu(刘旭)",
		},
		{
			Name:     "服务状态",
			Key:      "status.conditions",
			Type:     annotation.JSONType,
			ReadOnly: true,
			Document: `
k8s < 1.20 时，用来存储同步状态数据。
`,
			RequireVersion: ">=v2.1.3",
			Commit:         "32fa0fc750e3935304fada9d6fca93c4304a1bd6",
			Author:         "misakazhou(周宏宇)",
		},
		{
			Name:     "同步开始时间",
			Key:      "sync-begin-time",
			Type:     annotation.TimeType,
			ReadOnly: true,
			Document: `
同步开始时间。
`,
			InternalDocURL: "https://tapd.woa.com/tapd_fe/70108010/story/detail/1070108010121181667?from_iteration_id=1070108010002060467",
			RequireVersion: ">=v2.5.1",
			Author:         "wallaceqian(钱成龙)",
		},
		{
			Name:     "同步结束时间",
			Key:      "sync-end-time",
			Type:     annotation.TimeType,
			ReadOnly: true,
			Document: `
同步结束时间。
`,
			InternalDocURL: "https://tapd.woa.com/tapd_fe/70108010/story/detail/1070108010121181667?from_iteration_id=1070108010002060467",
			RequireVersion: ">=v2.5.1",
			Author:         "wallaceqian(钱成龙)",
		},
		{
			Name: "指定同步模式",
			Key:  "mode",
			Type: annotation.EnumType,
			Document: `
指定同步模式。

**可选值：**
- skip(>=v2.4.0)：跳过同步，不进行任何操作
- dry-run(>=v2.5.1 or =2.4.4)：模拟同步，不会触发真实修改操作
`,
			ExampleValue:   "skip",
			RequireVersion: ">=v2.4.0",
			Author:         "wallaceqian(钱成龙)",
		},
		{
			Name:   "删除保护",
			Key:    "deletion-protection",
			Type:   annotation.BoolType,
			Public: false,
			Document: `
配置 CLB 删除保护，开启后将无法删除集群内 Service 资源，同时无法通过 CLB 控制台或 API 删除负载均衡实例。
`,
			InternalDocURL: "https://iwiki.woa.com/p/4013775361",
			ExampleValue:   "true",
			RequireVersion: ">=v2.6.0",
			Author:         "bitliu(刘训灼)",
		},
		{
			Name:   "错误注入",
			Key:    "chaos-errorcode",
			Type:   annotation.StringType,
			Public: false,
			Document: `
手动注入错误码，模拟错误事件、指标以及资源异常状态。
`,
			InternalDocURL: "https://iwiki.woa.com/p/4014079858",
			ExampleValue:   "E5012",
			RequireVersion: ">=v2.6.0",
			Author:         "bitliu(刘训灼)",
		},
		{
			Name:   "强制删除",
			Key:    "force-delete",
			Type:   annotation.BoolType,
			Public: false,
			Document: `
当触发 CLB 删除拦截规则时，提供强制删除 CLB 实例开关。
`,
			InternalDocURL: "https://iwiki.woa.com/p/4014329013",
			ExampleValue:   "true",
			RequireVersion: ">=v2.6.0",
			Author:         "bitliu(刘训灼)",
		},
		{
			Name: "指定service绑定的endpoints",
			Key:  "loadbalancer-source-endpoints",
			Type: annotation.StringType,
			Document: `
指定service绑定的endpoints，仅限no-selector service使用。
`,
			InternalDocURL: "https://tapd.woa.com/tapd_fe/70108010/story/detail/1070108010122372478",
			ExampleValue:   "kubernetes",
			RequireVersion: ">=v2.5.3",
			Author:         "qingyangwu(武浩)",
		},
		{
			Name:   "保留CLB实例",
			Key:    "preserve-clb-on-deletion",
			Type:   annotation.BoolType,
			Public: true,
			Document: `
删除Service时保留TKE自动创建的CLB实例。开启后，删除Service时将清理监听器和后端配置，但保留CLB实例本身，以便后续复用。
仅对TKE自动创建的CLB生效，使用已有CLB的Service不受影响。

**注意：**
- 该功能与删除保护功能兼容，删除保护优先级更高
- 保留的CLB实例可通过已有CLB注解被其他Service复用
- 保留CLB时会清理相关的TKE标签和配置
`,
			ExampleValue:   "true",
			RequireVersion: ">=v2.7.0",
			Author:         "bitliu(刘训灼)",
		},
	})
}
